import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useUserStore } from '@/domains/user/stores/userStore';
import { useNutritionStore } from '@/domains/nutrition/stores/nutritionStore';
import { calculateSnackCalories } from '@/shared/utils/snackTimeUtils';
import { geminiService } from '@/infrastructure/ai';
import { formatNumber, formatCalorieData, formatNutritionData, formatMetabolicValue, formatProfileData } from '@/shared/utils/format';
import { CalorieDisplay } from '@/shared/components/atoms';

import BottomNavigation from '@/shared/components/navigation/BottomNavigationNew';


const DashboardPage: React.FC = () => {
  const navigate = useNavigate();
  const { profile } = useUserStore();
  const { getDailySummary, updateDailySummary, addDetailedFoodRecord } = useNutritionStore();
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [aiAdvice, setAiAdvice] = useState<string | null>(null);

  // 移除Anime.js相关的refs



  const today = new Date();
  const todaySummary = getDailySummary(today);



  // Anime.js v4兼容动画系统 - 确保不影响底部导航栏
  useEffect(() => {
    // 导入Anime.js动画函数
    import('@/utils/animations').then(({ createPageLoadAnimation }) => {
      // 页面加载动画 - 使用opacity避免transform影响fixed定位
      const cardElements = [
        '.dashboard-card',
        '.action-button',
        '.hero-section',
        '.nutrition-card',
        '.profile-card'
      ];

      // 创建页面加载动画
      createPageLoadAnimation(cardElements);

      // 添加数字递增动画
      setTimeout(async () => {
        const { createCountUpAnimation, createProgressAnimation } = await import('@/utils/animations');

        // 卡路里数字动画
        const calorieElements = document.querySelectorAll('.calorie-number');
        calorieElements.forEach((element: Element) => {
          const htmlElement = element as HTMLElement;
          const targetValue = parseInt(htmlElement.textContent || '0');
          if (targetValue > 0) {
            createCountUpAnimation(htmlElement, targetValue, 1200);
          }
        });

        // 进度条动画
        const progressBars = document.querySelectorAll('.progress-bar');
        progressBars.forEach((element: Element) => {
          const htmlElement = element as HTMLElement;
          const targetWidth = parseFloat(htmlElement.style.width || '0');
          if (targetWidth > 0) {
            createProgressAnimation(htmlElement, targetWidth);
          }
        });

        // 验证底部导航栏位置未受影响
        const dock = document.querySelector('.dock') as HTMLElement;
        if (dock) {
          const computedStyle = window.getComputedStyle(dock);
          console.log('🔍 Dock验证 - Anime.js动画后:', {
            position: computedStyle.position,
            bottom: computedStyle.bottom,
            zIndex: computedStyle.zIndex,
            transform: computedStyle.transform
          });
        }
      }, 500);
    });
  }, []);



  // 初始化今日营养数据
  useEffect(() => {
    if (profile && !todaySummary) {
      // 创建默认的每日汇总
      const calorieLimit = typeof profile.dailyCalorieLimit === 'string'
        ? parseInt(profile.dailyCalorieLimit)
        : profile.dailyCalorieLimit;



      updateDailySummary(today, {
        totalCalories: 0,
        calorieLimit: calorieLimit,
        remainingCalories: calorieLimit,
        mealBreakdown: {
          breakfast: {
            mealType: 'breakfast',
            calories: 0,
            calorieLimit: Math.round(profile.dailyCalorieLimit * profile.mealRatios.breakfast),
            foodCount: 0,
            percentage: 0
          },
          lunch: {
            mealType: 'lunch',
            calories: 0,
            calorieLimit: Math.round(profile.dailyCalorieLimit * profile.mealRatios.lunch),
            foodCount: 0,
            percentage: 0
          },
          dinner: {
            mealType: 'dinner',
            calories: 0,
            calorieLimit: Math.round(profile.dailyCalorieLimit * profile.mealRatios.dinner),
            foodCount: 0,
            percentage: 0
          },
          snack: {
            mealType: 'snack',
            calories: 0,
            calorieLimit: calculateSnackCalories(profile.dailyCalorieLimit).totalSnackCalories,
            foodCount: 0,
            percentage: 0
          }
        },
        nutrition: {
          protein: 0,
          fat: 0,
          carbs: 0,
          fiber: 0,
          sugar: 0,
          sodium: 0
        },
        status: 'under',
        percentage: 0
      });
    }
  }, [profile, todaySummary, updateDailySummary, today]);

  // AI营养分析功能 - 集成AURA-X协议
  const handleAIAnalysis = async () => {
    if (!todaySummary || !profile) {
      console.log('AI分析失败：缺少必要数据', { todaySummary, profile });
      return;
    }

    // AURA-X协议：使用context7-mcp获取最新营养学知识
    // 这里可以集成最新的营养指导原则和健康建议

    console.log('开始AI营养分析...');
    setIsAnalyzing(true);
    try {
      // 构建营养数据分析提示
      const nutritionData = {
        totalCalories: todaySummary.totalCalories,
        calorieLimit: profile.dailyCalorieLimit,
        protein: todaySummary.nutrition.protein,
        fat: todaySummary.nutrition.fat,
        carbs: todaySummary.nutrition.carbs,
        fiber: todaySummary.nutrition.fiber,
        sugar: todaySummary.nutrition.sugar,
        sodium: todaySummary.nutrition.sodium,
        userProfile: {
          age: profile.age,
          gender: profile.gender,
          weight: profile.weight,
          height: profile.height,
          targetWeight: profile.targetWeight,
          activityLevel: profile.activityLevel,
          bmr: profile.bmr,
          tdee: profile.tdee
        }
      };

      // 专门的营养建议提示词 - 与食物识别完全区分
      const nutritionAnalysisPrompt = `
你是一位专业的营养师和减重顾问，请基于用户的个人信息和今日营养摄入数据，提供个性化的营养分析和减重建议。

用户档案：
年龄：${nutritionData.userProfile.age}岁
性别：${nutritionData.userProfile.gender}
当前体重：${formatProfileData(nutritionData.userProfile.weight, 'kg')}
身高：${nutritionData.userProfile.height}cm
目标体重：${formatProfileData(nutritionData.userProfile.targetWeight, 'kg')}
活动水平：${nutritionData.userProfile.activityLevel}
基础代谢率(BMR)：${formatMetabolicValue(nutritionData.userProfile.bmr, 'kcal/天')}
总日消耗(TDEE)：${formatMetabolicValue(nutritionData.userProfile.tdee, 'kcal/天')}

今日营养摄入：
卡路里摄入：${formatCalorieData(nutritionData.totalCalories)} / ${formatCalorieData(nutritionData.calorieLimit)}
蛋白质：${formatNutritionData(nutritionData.protein, 'g')}
脂肪：${formatNutritionData(nutritionData.fat, 'g')}
碳水化合物：${formatNutritionData(nutritionData.carbs, 'g')}
膳食纤维：${formatNutritionData(nutritionData.fiber, 'g')}
糖分：${formatNutritionData(nutritionData.sugar, 'g')}
钠：${formatNutritionData(nutritionData.sodium, 'mg')}

请提供专业分析，包含以下内容：
1. 营养状况评估：分析当前摄入是否均衡，是否有营养素过量或不足
2. 减重进度评价：基于卡路里摄入与TDEE的关系，评估减重效果
3. 改善建议：针对营养不均衡的具体改善方案
4. 健康提醒：基于钠、糖等指标的健康风险提示

重要格式要求：
- 请使用HTML格式进行排版，使用<br>换行，<strong>加粗重点，<p>分段
- 不要使用Markdown格式（如#、**、*等符号）
- 控制在200字以内，重点突出减重和健康改善建议
- 使用专业但易懂的中文表达

请按照以下HTML格式回复：
<p><strong>营养评估：</strong>[简要评估]</p>
<p><strong>减重进度：</strong>[进度分析]</p>
<p><strong>改善建议：</strong>[具体建议]</p>
<p><strong>健康提醒：</strong>[风险提示]</p>
      `;

      console.log('发送AI分析请求，提示词长度：', nutritionAnalysisPrompt.length);

      // 使用专门的营养建议方法，温度参数为0.7
      const result = await geminiService.analyzeNutritionAdvice(nutritionAnalysisPrompt);

      console.log('AI分析响应：', result);

      // 直接使用返回的建议文本
      if (result && result.advice && result.advice.trim()) {
        console.log('AI建议内容：', result.advice);
        setAiAdvice(result.advice);
      } else {
        console.log('AI响应为空或无效，使用默认建议');
        setAiAdvice('AI分析完成，建议保持当前的营养摄入平衡。');
      }
    } catch (error) {
      console.error('AI分析失败:', error);
      console.error('错误详情:', {
        message: error instanceof Error ? error.message : '未知错误',
        stack: error instanceof Error ? error.stack : undefined
      });
      setAiAdvice(`AI分析失败：${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      console.log('AI分析流程结束');
      setIsAnalyzing(false);
    }
  };

  // 终止AI分析
  const handleStopAnalysis = () => {
    setIsAnalyzing(false);
    setAiAdvice('分析已终止');
  };

  const handleCardHover = (e: React.MouseEvent<HTMLDivElement>) => {
    // Anime.js v4卡片hover动画 - 兼容底部导航栏
    const element = e.currentTarget as HTMLElement;
    element.classList.add('card-hover-target');
    import('@/utils/animations').then(({ createCardHoverAnimation }) => {
      createCardHoverAnimation('.card-hover-target', true);
    });
  };

  const handleCardLeave = (e: React.MouseEvent<HTMLDivElement>) => {
    // Anime.js v4卡片leave动画
    const element = e.currentTarget as HTMLElement;
    element.classList.add('card-leave-target');
    import('@/utils/animations').then(({ createCardHoverAnimation }) => {
      createCardHoverAnimation('.card-leave-target', false);
    });
    setTimeout(() => {
      element.classList.remove('card-hover-target', 'card-leave-target');
    }, 350);
  };



  if (!profile) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50 flex items-center justify-center p-4">
        <div className="text-center">
          <div className="w-16 h-16 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-2xl mx-auto mb-4 flex items-center justify-center">
            <span className="text-2xl">👤</span>
          </div>
          <h2 className="text-xl font-bold text-slate-800 mb-2">
            未找到用户档案
          </h2>
          <p className="text-slate-600 mb-4">
            请先完成个人档案设置
          </p>
          <button
            className="btn btn-primary btn-lg"
            onClick={() => navigate('/setup')}
          >
            立即设置
          </button>
        </div>
      </div>
    );
  }





  return (
    <div className="relative">
      {/* 主要内容容器 - 完全避免transform，确保不影响fixed定位 */}
      <div
        className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50"
        style={{
          // 强制确保没有任何transform属性
          transform: 'none',
          // 避免创建层叠上下文
          isolation: 'auto',
          // 确保不影响fixed定位的子元素
          position: 'relative',
          zIndex: 'auto'
        }}
      >
        {/* 移动端优先的容器设计 */}
        <div className="w-full max-w-none sm:max-w-lg md:max-w-2xl lg:max-w-4xl xl:max-w-6xl mx-auto px-4 py-6 sm:px-6 lg:px-8">
          {/* 今日营养概览 - 移动端优先设计 */}
          {todaySummary && (
            <div className="space-y-4 sm:space-y-6">
              {/* 目标倒计时卡片 */}
              {profile && (
                <div
                  className="dashboard-card relative overflow-hidden bg-gradient-to-br from-indigo-50 via-purple-50 to-pink-50 rounded-2xl sm:rounded-3xl p-4 sm:p-6 border border-indigo-200/50 shadow-xl"
                  onMouseEnter={handleCardHover}
                  onMouseLeave={handleCardLeave}
                >
                  {/* 背景装饰 */}
                  <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-indigo-200/20 to-purple-200/20 rounded-full -translate-y-16 translate-x-16"></div>
                  <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-pink-200/20 to-indigo-200/20 rounded-full translate-y-12 -translate-x-12"></div>

                  <div className="relative z-10">
                    {/* 标题区域 */}
                    <div className="text-center mb-4">
                      <div className="inline-flex items-center gap-3 mb-3">
                        <div className="w-10 h-10 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-xl flex items-center justify-center shadow-lg">
                          <span className="text-xl">🎯</span>
                        </div>
                        <h3 className="text-xl sm:text-2xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">
                          目标倒计时
                        </h3>
                      </div>
                    </div>

                    {/* 倒计时数据 */}
                    <div className="grid grid-cols-3 gap-4">
                      <div className="bg-white/80 backdrop-blur-sm rounded-xl p-3 text-center shadow-lg border border-white/50">
                        <div className="text-2xl font-bold text-indigo-600 mb-1">
                          {(() => {
                            // 计算剩余天数（假设从档案创建时开始计算）
                            const startDate = profile.createdAt ? new Date(profile.createdAt) : new Date();
                            const today = new Date();
                            const daysPassed = Math.floor((today.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
                            const remainingDays = Math.max(0, profile.targetDays - daysPassed);
                            return remainingDays;
                          })()}
                        </div>
                        <div className="text-xs text-slate-600">剩余天数</div>
                      </div>
                      <div className="bg-white/80 backdrop-blur-sm rounded-xl p-3 text-center shadow-lg border border-white/50">
                        <div className="text-2xl font-bold text-purple-600 mb-1">
                          {formatProfileData(profile.weight - profile.targetWeight)}
                        </div>
                        <div className="text-xs text-slate-600">目标减重</div>
                        <div className="text-xs text-purple-500">kg</div>
                      </div>
                      <div className="bg-white/80 backdrop-blur-sm rounded-xl p-3 text-center shadow-lg border border-white/50">
                        <div className="text-2xl font-bold text-pink-600 mb-1">
                          {formatProfileData(((profile.weight - profile.targetWeight) / profile.targetDays) * 7)}
                        </div>
                        <div className="text-xs text-slate-600">周减重</div>
                        <div className="text-xs text-pink-500">kg/周</div>
                      </div>
                    </div>

                    {/* 进度条 */}
                    <div className="mt-4">
                      <div className="flex items-center justify-between text-sm text-slate-600 mb-2">
                        <span>目标进度</span>
                        <span className="font-bold text-indigo-600">
                          {(() => {
                            const startDate = profile.createdAt ? new Date(profile.createdAt) : new Date();
                            const today = new Date();
                            const daysPassed = Math.floor((today.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
                            const progress = Math.min(100, Math.max(0, (daysPassed / profile.targetDays) * 100));
                            return formatNumber(progress, { precision: 'decimal' });
                          })()}% 完成
                        </span>
                      </div>
                      <div className="w-full bg-white/60 rounded-full h-2 shadow-inner">
                        <div
                          className="bg-gradient-to-r from-indigo-500 to-purple-500 h-2 rounded-full shadow-sm transition-all duration-1000 ease-out"
                          style={{
                            width: `${(() => {
                              const startDate = profile.createdAt ? new Date(profile.createdAt) : new Date();
                              const today = new Date();
                              const daysPassed = Math.floor((today.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
                              const progress = Math.min(100, Math.max(0, (daysPassed / profile.targetDays) * 100));
                              return formatNumber(progress, { precision: 'decimal' });
                            })()}%`
                          }}
                        ></div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* 今日卡路里 - 炫酷重新设计 */}
              <div
                className="dashboard-card relative overflow-hidden bg-gradient-to-br from-orange-50 via-red-50 to-pink-50 rounded-2xl sm:rounded-3xl p-4 sm:p-6 border border-orange-200/50 shadow-xl"
                onMouseEnter={handleCardHover}
                onMouseLeave={handleCardLeave}
              >
                {/* 背景装饰 */}
                <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-orange-200/20 to-red-200/20 rounded-full -translate-y-16 translate-x-16"></div>
                <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-pink-200/20 to-orange-200/20 rounded-full translate-y-12 -translate-x-12"></div>

                <div className="relative z-10">
                  {/* 标题区域 */}
                  <div className="text-center mb-6">
                    <div className="inline-flex items-center gap-3 mb-3">
                      <div className="w-10 h-10 bg-gradient-to-r from-orange-500 to-red-500 rounded-xl flex items-center justify-center shadow-lg">
                        <span className="text-xl">🔥</span>
                      </div>
                      <h3 className="text-xl sm:text-2xl font-bold bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent">
                        今日卡路里
                      </h3>
                    </div>

                    {/* 进度指示器 */}
                    <div className="w-full bg-white/60 rounded-full h-3 mb-3 shadow-inner">
                      <div
                        className="progress-bar bg-gradient-to-r from-orange-500 to-red-500 h-3 rounded-full shadow-sm transition-all duration-1000 ease-out"
                        style={{ width: `${Math.min((todaySummary.totalCalories / todaySummary.calorieLimit) * 100, 100)}%` }}
                      ></div>
                    </div>

                    <div className="flex items-center justify-between text-sm text-slate-600 mb-2">
                      <span>
                        {todaySummary.totalCalories >= todaySummary.calorieLimit ? (
                          <span className="text-red-600 font-bold">
                            已超标 {formatCalorieData(todaySummary.totalCalories - todaySummary.calorieLimit).replace(' kcal', '')} 卡路里
                          </span>
                        ) : (
                          <>
                            还可摄入 <span className={`font-bold ${
                              (todaySummary.calorieLimit - todaySummary.totalCalories) <= 200
                                ? 'text-red-600'
                                : 'text-emerald-600'
                            }`}>
                              <CalorieDisplay value={todaySummary.calorieLimit - todaySummary.totalCalories} className="" />
                            </span>
                          </>
                        )}
                      </span>
                      <span className={`font-bold ${
                        todaySummary.totalCalories >= todaySummary.calorieLimit
                          ? 'text-red-600'
                          : 'text-orange-600'
                      }`}>
                        {formatNumber((todaySummary.totalCalories / todaySummary.calorieLimit) * 100, { precision: 'integer', suffix: '%' })}
                      </span>
                    </div>
                  </div>

                  {/* 数据展示区域 */}
                  <div className="grid grid-cols-2 gap-4 mb-4">
                    <div className="bg-white/80 backdrop-blur-sm rounded-xl p-4 text-center shadow-lg border border-white/50">
                      <div className="calorie-number text-2xl sm:text-3xl font-bold text-orange-600 mb-1">
                        <CalorieDisplay value={todaySummary.totalCalories} className="" />
                      </div>
                      <div className="text-xs text-slate-600 font-medium">已摄入</div>
                    </div>
                    <div className="bg-white/80 backdrop-blur-sm rounded-xl p-4 text-center shadow-lg border border-white/50">
                      <div className="calorie-number text-2xl sm:text-3xl font-bold text-red-600 mb-1">
                        <CalorieDisplay value={todaySummary.calorieLimit} />
                      </div>
                      <div className="text-xs text-slate-600 font-medium">目标</div>
                    </div>
                  </div>



                </div>
              </div>

              {/* 三餐分配 - 独立卡片 */}
              <div
                className="dashboard-card relative overflow-hidden bg-gradient-to-br from-violet-50 via-purple-50 to-indigo-50 rounded-2xl sm:rounded-3xl p-4 sm:p-6 border border-violet-200/50 shadow-xl"
                onMouseEnter={handleCardHover}
                onMouseLeave={handleCardLeave}
              >
                {/* 背景装饰 */}
                <div className="absolute top-0 right-0 w-36 h-36 bg-gradient-to-br from-violet-200/20 to-purple-200/20 rounded-full -translate-y-18 translate-x-18"></div>
                <div className="absolute bottom-0 left-0 w-32 h-32 bg-gradient-to-tr from-indigo-200/20 to-violet-200/20 rounded-full translate-y-16 -translate-x-16"></div>

                <div className="relative z-10">
                  {/* 标题区域 */}
                  <div className="flex items-center gap-3 mb-6">
                    <div className="w-10 h-10 bg-gradient-to-r from-violet-500 to-purple-500 rounded-xl flex items-center justify-center shadow-lg">
                      <span className="text-xl">🍴</span>
                    </div>
                    <h3 className="text-xl sm:text-2xl font-bold bg-gradient-to-r from-violet-600 to-purple-600 bg-clip-text text-transparent">
                      营养计划
                    </h3>
                  </div>

                  {/* 三餐网格 */}
                  <div className="grid grid-cols-1 gap-4">
                    {/* 早餐 */}
                    <div className="bg-gradient-to-r from-amber-50 to-orange-50 rounded-xl p-4 border border-amber-200/50 shadow-lg">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className="w-8 h-8 bg-gradient-to-r from-amber-400 to-orange-400 rounded-lg flex items-center justify-center shadow-md">
                            <span className="text-sm">🌅</span>
                          </div>
                          <div>
                            <div className="font-bold text-amber-800 text-sm sm:text-base">早餐</div>
                            <div className="text-xs text-amber-600">开启美好一天</div>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="text-lg sm:text-xl font-bold text-amber-700">
                            <CalorieDisplay value={todaySummary.mealBreakdown.breakfast.calories} /> / <CalorieDisplay value={profile.dailyCalorieLimit * 0.3} />
                          </div>
                          <div className="text-xs text-amber-600">
                            {formatNumber((todaySummary.mealBreakdown.breakfast.calories / (profile.dailyCalorieLimit * 0.3)) * 100, { precision: 'integer', suffix: '%' })}
                          </div>
                        </div>
                      </div>
                      {/* 早餐进度条 */}
                      <div className="mt-3 w-full bg-amber-100 rounded-full h-2">
                        <div
                          className="bg-gradient-to-r from-amber-400 to-amber-600 h-2 rounded-full transition-all duration-1000 ease-out"
                          style={{ width: `${Math.min((todaySummary.mealBreakdown.breakfast.calories / (profile.dailyCalorieLimit * 0.3)) * 100, 100)}%` }}
                        ></div>
                      </div>
                    </div>

                    {/* 午餐 */}
                    <div className="bg-gradient-to-r from-sky-50 to-blue-50 rounded-xl p-4 border border-sky-200/50 shadow-lg">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className="w-8 h-8 bg-gradient-to-r from-sky-400 to-blue-400 rounded-lg flex items-center justify-center shadow-md">
                            <span className="text-sm">☀️</span>
                          </div>
                          <div>
                            <div className="font-bold text-sky-800 text-sm sm:text-base">午餐</div>
                            <div className="text-xs text-sky-600">补充能量</div>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="text-lg sm:text-xl font-bold text-sky-700">
                            <CalorieDisplay value={todaySummary.mealBreakdown.lunch.calories} /> / <CalorieDisplay value={profile.dailyCalorieLimit * 0.4} />
                          </div>
                          <div className="text-xs text-sky-600">
                            {formatNumber((todaySummary.mealBreakdown.lunch.calories / (profile.dailyCalorieLimit * 0.4)) * 100, { precision: 'integer', suffix: '%' })}
                          </div>
                        </div>
                      </div>
                      {/* 午餐进度条 */}
                      <div className="mt-3 w-full bg-sky-100 rounded-full h-2">
                        <div
                          className="bg-gradient-to-r from-sky-400 to-sky-600 h-2 rounded-full transition-all duration-1000 ease-out"
                          style={{ width: `${Math.min((todaySummary.mealBreakdown.lunch.calories / (profile.dailyCalorieLimit * 0.4)) * 100, 100)}%` }}
                        ></div>
                      </div>
                    </div>

                    {/* 晚餐 */}
                    <div className="bg-gradient-to-r from-indigo-50 to-purple-50 rounded-xl p-4 border border-indigo-200/50 shadow-lg">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className="w-8 h-8 bg-gradient-to-r from-indigo-400 to-purple-400 rounded-lg flex items-center justify-center shadow-md">
                            <span className="text-sm">🌙</span>
                          </div>
                          <div>
                            <div className="font-bold text-indigo-800 text-sm sm:text-base">晚餐</div>
                            <div className="text-xs text-indigo-600">营养收尾</div>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="text-lg sm:text-xl font-bold text-indigo-700">
                            <CalorieDisplay value={todaySummary.mealBreakdown.dinner.calories} /> / <CalorieDisplay value={profile.dailyCalorieLimit * 0.3} />
                          </div>
                          <div className="text-xs text-indigo-600">
                            {formatNumber((todaySummary.mealBreakdown.dinner.calories / (profile.dailyCalorieLimit * 0.3)) * 100, { precision: 'integer', suffix: '%' })}
                          </div>
                        </div>
                      </div>
                      {/* 晚餐进度条 */}
                      <div className="mt-3 w-full bg-indigo-100 rounded-full h-2">
                        <div
                          className="bg-gradient-to-r from-indigo-400 to-indigo-600 h-2 rounded-full transition-all duration-1000 ease-out"
                          style={{ width: `${Math.min((todaySummary.mealBreakdown.dinner.calories / (profile.dailyCalorieLimit * 0.3)) * 100, 100)}%` }}
                        ></div>
                      </div>
                    </div>

                    {/* 加餐 - 简化显示 */}
                    {(() => {
                      const snackCalories = calculateSnackCalories(profile.dailyCalorieLimit);

                      return (
                        <div className="bg-gradient-to-r from-emerald-50 to-teal-50 rounded-xl p-4 border border-emerald-200/50 shadow-lg">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                              <div className="w-8 h-8 bg-gradient-to-r from-emerald-400 to-teal-400 rounded-lg flex items-center justify-center shadow-md">
                                <span className="text-sm">🍎</span>
                              </div>
                              <div>
                                <div className="font-bold text-emerald-800 text-sm sm:text-base">
                                  加餐
                                </div>
                                <div className="text-xs text-emerald-600">
                                  全天加餐总计
                                </div>
                              </div>
                            </div>
                            <div className="text-right">
                              <div className="text-lg sm:text-xl font-bold text-emerald-700">
                                {formatCalorieData(todaySummary.mealBreakdown.snack.calories).replace(' kcal', '')} / {snackCalories.totalSnackCalories} kcal
                              </div>
                              <div className="text-xs text-emerald-600">
                                {formatNumber((todaySummary.mealBreakdown.snack.calories / snackCalories.totalSnackCalories) * 100, { precision: 'integer', suffix: '%' })}
                              </div>
                            </div>
                          </div>
                          {/* 加餐进度条 */}
                          <div className="mt-3 w-full bg-emerald-100 rounded-full h-2">
                            <div
                              className="bg-gradient-to-r from-emerald-400 to-emerald-600 h-2 rounded-full transition-all duration-1000 ease-out"
                              style={{ width: `${Math.min((todaySummary.mealBreakdown.snack.calories / snackCalories.totalSnackCalories) * 100, 100)}%` }}
                            ></div>
                          </div>

                        </div>
                      );
                    })()}
                  </div>
                </div>
              </div>

              {/* 营养详情 - 独立卡片 */}
              <div
                className="dashboard-card relative overflow-hidden bg-gradient-to-br from-emerald-50 via-teal-50 to-cyan-50 rounded-2xl sm:rounded-3xl p-4 sm:p-6 border border-emerald-200/50 shadow-xl"
                onMouseEnter={handleCardHover}
                onMouseLeave={handleCardLeave}
              >
                {/* 背景装饰 */}
                <div className="absolute top-0 left-0 w-40 h-40 bg-gradient-to-br from-emerald-200/20 to-teal-200/20 rounded-full -translate-y-20 -translate-x-20"></div>
                <div className="absolute bottom-0 right-0 w-28 h-28 bg-gradient-to-tl from-cyan-200/20 to-emerald-200/20 rounded-full translate-y-14 translate-x-14"></div>

                <div className="relative z-10">
                  {/* 标题区域 */}
                  <div className="flex items-center gap-3 mb-6">
                    <div className="w-10 h-10 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-xl flex items-center justify-center shadow-lg">
                      <span className="text-xl">📊</span>
                    </div>
                    <h3 className="text-xl sm:text-2xl font-bold bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent">
                      营养详情
                    </h3>
                  </div>

                  {/* 营养素网格 */}
                  <div className="grid grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4">
                    {/* 蛋白质 */}
                    <div className="bg-white/80 backdrop-blur-sm rounded-xl p-3 sm:p-4 shadow-lg border border-white/50">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium text-slate-600">蛋白质</span>
                        <span className="text-xs text-emerald-600">🥩</span>
                      </div>
                      <div className="text-xl sm:text-2xl font-bold text-emerald-600 mb-1">
                        {formatNutritionData(todaySummary.nutrition.protein, 'g')}
                      </div>
                      <div className="w-full bg-emerald-100 rounded-full h-2">
                        <div className="bg-gradient-to-r from-emerald-400 to-emerald-600 h-2 rounded-full" style={{ width: `${Math.min((todaySummary.nutrition.protein / 150) * 100, 100)}%` }}></div>
                      </div>
                    </div>

                    {/* 脂肪 */}
                    <div className="bg-white/80 backdrop-blur-sm rounded-xl p-3 sm:p-4 shadow-lg border border-white/50">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium text-slate-600">脂肪</span>
                        <span className="text-xs text-amber-600">🥑</span>
                      </div>
                      <div className="text-xl sm:text-2xl font-bold text-amber-600 mb-1">
                        {formatNutritionData(todaySummary.nutrition.fat, 'g')}
                      </div>
                      <div className="w-full bg-amber-100 rounded-full h-2">
                        <div className="bg-gradient-to-r from-amber-400 to-amber-600 h-2 rounded-full" style={{ width: `${Math.min((todaySummary.nutrition.fat / 80) * 100, 100)}%` }}></div>
                      </div>
                    </div>

                    {/* 碳水化合物 */}
                    <div className="bg-white/80 backdrop-blur-sm rounded-xl p-3 sm:p-4 shadow-lg border border-white/50">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium text-slate-600">碳水</span>
                        <span className="text-xs text-blue-600">🍞</span>
                      </div>
                      <div className="text-xl sm:text-2xl font-bold text-blue-600 mb-1">
                        {formatNutritionData(todaySummary.nutrition.carbs, 'g')}
                      </div>
                      <div className="w-full bg-blue-100 rounded-full h-2">
                        <div className="bg-gradient-to-r from-blue-400 to-blue-600 h-2 rounded-full" style={{ width: `${Math.min((todaySummary.nutrition.carbs / 300) * 100, 100)}%` }}></div>
                      </div>
                    </div>

                    {/* 纤维 */}
                    <div className="bg-white/80 backdrop-blur-sm rounded-xl p-3 sm:p-4 shadow-lg border border-white/50">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium text-slate-600">纤维</span>
                        <span className="text-xs text-green-600">🥬</span>
                      </div>
                      <div className="text-xl sm:text-2xl font-bold text-green-600 mb-1">
                        {formatNutritionData(todaySummary.nutrition.fiber, 'g')}
                      </div>
                      <div className="w-full bg-green-100 rounded-full h-2">
                        <div className="bg-gradient-to-r from-green-400 to-green-600 h-2 rounded-full" style={{ width: `${Math.min((todaySummary.nutrition.fiber / 35) * 100, 100)}%` }}></div>
                      </div>
                    </div>

                    {/* 糖分 */}
                    <div className="bg-white/80 backdrop-blur-sm rounded-xl p-3 sm:p-4 shadow-lg border border-white/50">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium text-slate-600">糖分</span>
                        <span className="text-xs text-pink-600">🍯</span>
                      </div>
                      <div className="text-xl sm:text-2xl font-bold text-pink-600 mb-1">
                        {formatNutritionData(todaySummary.nutrition.sugar || 0, 'g')}
                      </div>
                      <div className="w-full bg-pink-100 rounded-full h-2">
                        <div className="bg-gradient-to-r from-pink-400 to-pink-600 h-2 rounded-full" style={{ width: `${Math.min(((todaySummary.nutrition.sugar || 0) / 50) * 100, 100)}%` }}></div>
                      </div>
                    </div>

                    {/* 钠 */}
                    <div className="bg-white/80 backdrop-blur-sm rounded-xl p-3 sm:p-4 shadow-lg border border-white/50">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium text-slate-600">钠</span>
                        <span className="text-xs text-purple-600">🧂</span>
                      </div>
                      <div className="text-xl sm:text-2xl font-bold text-purple-600 mb-1">
                        {formatNutritionData(todaySummary.nutrition.sodium || 0, 'mg')}
                      </div>
                      <div className="w-full bg-purple-100 rounded-full h-2">
                        <div className="bg-gradient-to-r from-purple-400 to-purple-600 h-2 rounded-full" style={{ width: `${Math.min(((todaySummary.nutrition.sodium || 0) / 2300) * 100, 100)}%` }}></div>
                      </div>
                    </div>
                  </div>

                  {/* AI营养分析按钮 */}
                  <div className="mt-4">
                    <button
                      onClick={() => isAnalyzing ? handleStopAnalysis() : handleAIAnalysis()}
                      className={`w-full p-3 text-white rounded-xl shadow-lg transition-all duration-200 hover:shadow-xl whitespace-nowrap ${
                        isAnalyzing
                          ? 'bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700'
                          : 'bg-gradient-to-r from-emerald-500 to-teal-500 hover:from-emerald-600 hover:to-teal-600'
                      }`}
                    >
                      <div className="flex items-center justify-center">
                        {isAnalyzing ? (
                          <span className="font-medium">终止分析</span>
                        ) : (
                          <>
                            <span className="text-lg mr-2 flex-shrink-0">🤖</span>
                            <span className="font-medium">查看AI营养建议</span>
                          </>
                        )}
                      </div>
                    </button>

                    {/* AI建议显示 */}
                    {aiAdvice && (
                      <div className="mt-3 p-3 bg-emerald-50 border border-emerald-200 rounded-xl">
                        <div className="flex items-start gap-2">
                          <span className="text-emerald-500 text-lg">🤖</span>
                          <div className="flex-1">
                            <div className="text-sm font-medium text-emerald-800 mb-1">AI营养建议</div>
                            <div
                              className="text-sm text-emerald-700"
                              dangerouslySetInnerHTML={{ __html: aiAdvice }}
                            />
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* 代谢信息 - 独立卡片 */}
              <div
                className="dashboard-card relative overflow-hidden bg-gradient-to-br from-rose-50 via-pink-50 to-red-50 rounded-2xl sm:rounded-3xl p-4 sm:p-6 border border-rose-200/50 shadow-xl"
                onMouseEnter={handleCardHover}
                onMouseLeave={handleCardLeave}
              >
                {/* 背景装饰 */}
                <div className="absolute top-0 right-0 w-36 h-36 bg-gradient-to-br from-rose-200/20 to-pink-200/20 rounded-full -translate-y-18 translate-x-18"></div>
                <div className="absolute bottom-0 left-0 w-32 h-32 bg-gradient-to-tr from-red-200/20 to-rose-200/20 rounded-full translate-y-16 -translate-x-16"></div>

                <div className="relative z-10">
                  {/* 标题区域 */}
                  <div className="flex items-center gap-3 mb-6">
                    <div className="w-10 h-10 bg-gradient-to-r from-rose-500 to-pink-500 rounded-xl flex items-center justify-center shadow-lg">
                      <span className="text-xl">⚡</span>
                    </div>
                    <h3 className="text-xl sm:text-2xl font-bold bg-gradient-to-r from-rose-600 to-pink-600 bg-clip-text text-transparent">
                      代谢信息
                    </h3>
                  </div>

                  {/* 代谢数据网格 */}
                  <div className="grid grid-cols-1 gap-4">
                    {/* BMR */}
                    <div className="bg-white/80 backdrop-blur-sm rounded-xl p-4 shadow-md border border-white/50">
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="text-sm text-slate-600 mb-1">基础代谢率 (BMR)</div>
                          <div className="text-xs text-slate-500">静息状态下的能量消耗</div>
                        </div>
                        <div className="text-right">
                          <div className="text-2xl font-bold text-rose-600">
                            <CalorieDisplay value={profile.bmr} className="" />
                          </div>
                          <div className="text-xs text-rose-500">/天</div>
                        </div>
                      </div>

                    </div>

                    {/* TDEE */}
                    <div className="bg-white/80 backdrop-blur-sm rounded-xl p-4 shadow-md border border-white/50">
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="text-sm text-slate-600 mb-1">总日消耗 (TDEE)</div>
                          <div className="text-xs text-slate-500">包含活动的总消耗</div>
                        </div>
                        <div className="text-right">
                          <div className="text-2xl font-bold text-pink-600">
                            <CalorieDisplay value={profile.tdee} className="" />
                          </div>
                          <div className="text-xs text-pink-500">/天</div>
                        </div>
                      </div>

                    </div>

                    {/* 代谢效率指示 */}
                    <div className="bg-gradient-to-r from-rose-100/50 to-pink-100/50 rounded-lg p-3 border border-rose-200/30">
                      <div className="flex items-center gap-2 text-sm">
                        <span className="text-rose-500">🔥</span>
                        <span className="text-slate-600">
                          代谢效率：<span className="font-semibold text-rose-600">
                            {formatNumber((profile.tdee / profile.bmr - 1) * 100, { precision: 'integer', suffix: '%' })}
                          </span> 活动加成
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>





              {/* 底部留白，避免内容被底部导航栏遮挡 - 适配全面屏安全区域 */}
              <div className="h-24 pb-safe"></div>
            </div>
          )}
        </div>
      </div>

      {/* 底部固定导航栏 - 移到根级别避免transform层叠上下文影响 */}
      <BottomNavigation
        showAddButton={true}
        onRecognitionComplete={(result: any) => {
          console.log('Dashboard收到识别结果:', result);

          // 处理OCR识别完成后的数据保存
          if (result && result.foods && result.foods.length > 0) {
            // 为每个识别的食物创建记录
            result.foods.forEach((food: any) => {
              const foodRecord = {
                name: food.name,
                weight: food.weight || 100, // 使用AI识别的重量，默认100g
                calories: food.calories,
                mealType: result.meal,
                recordedAt: new Date(),
                nutrition: food.nutrition || {
                  protein: Math.round(food.calories * 0.15 / 4), // 备用估算
                  fat: Math.round(food.calories * 0.25 / 9),
                  carbs: Math.round(food.calories * 0.6 / 4),
                  fiber: Math.round(food.calories * 0.05 / 4),
                  sugar: Math.round(food.calories * 0.1 / 4),
                  sodium: Math.round(food.calories * 0.5) // 钠含量估算(mg)
                },
                aiRecognition: {
                  confidence: food.confidence || 0.85, // 使用AI返回的置信度
                  method: result.method,
                  originalInput: result.content || ''
                },
                isEdited: false
              };

              // 添加到营养存储
              addDetailedFoodRecord(new Date(), foodRecord);
            });

            console.log('食物记录已保存到营养存储');
          }
        }}
      />
    </div>
  );
};

export default DashboardPage;