/**
 * 动画工具模块 - 基于 Anime.js 4 最新API
 * 提供流畅的UI动画效果，优化性能和用户体验
 */

import { createTimeline, animate, engine } from 'animejs';

// 配置全局动画设置
engine.defaults.duration = 500;
engine.defaults.frameRate = 60;

/**
 * 表单步骤切换动画
 * 为步骤切换提供流畅的过渡效果
 */
export const createStepTransition = (
  outgoingStep: string,
  incomingStep: string,
  direction: 'forward' | 'backward' = 'forward'
) => {
  const timeline = createTimeline({
    defaults: {
      ease: 'inOutExpo',
      duration: 400,
    }
  });

  // 退出动画
  timeline.add(outgoingStep, {
    x: direction === 'forward' ? '-100%' : '100%',
    opacity: 0,
    scale: 0.95,
  }, 0);

  // 进入动画
  timeline.add(incomingStep, {
    x: { from: direction === 'forward' ? '100%' : '-100%', to: '0%' },
    opacity: { from: 0, to: 1 },
    scale: { from: 0.95, to: 1 },
  }, 200);

  return timeline;
};

/**
 * 按钮交互动画
 * 为按钮点击提供微动画反馈
 */
export const createButtonAnimation = (target: string | HTMLElement) => {
  return animate(target, {
    scale: [1, 0.95, 1],
    duration: 200,
    ease: 'outElastic(1, .8)',
  });
};

/**
 * 输入框焦点动画
 * 为表单输入提供视觉反馈
 */
export const createInputFocusAnimation = (target: string | HTMLElement) => {
  return animate(target, {
    scale: [1, 1.02, 1],
    duration: 300,
    ease: 'outQuart',
  });
};

/**
 * 进度条动画
 * 为步骤指示器提供流畅的进度更新
 */
export const createProgressAnimation = (target: string | HTMLElement, progress: number) => {
  return animate(target, {
    width: `${progress}%`,
    duration: 600,
    ease: 'outExpo',
  });
};

/**
 * 错误提示动画
 * 为错误状态提供注意力吸引动画
 */
export const createErrorAnimation = (target: string | HTMLElement) => {
  return animate(target, {
    x: [0, -10, 10, -5, 5, 0],
    duration: 500,
    ease: 'outElastic(1, .8)',
  });
};

/**
 * 成功提示动画
 * 为成功状态提供愉悦的反馈动画
 */
export const createSuccessAnimation = (target: string | HTMLElement) => {
  const timeline = createTimeline({
    defaults: {
      ease: 'outElastic(1, .6)',
    }
  });

  timeline
    .add(target, {
      scale: [1, 1.1, 1],
      duration: 400,
    }, 0)
    .add(target, {
      rotate: [0, 5, -5, 0],
      duration: 300,
    }, 200);

  return timeline;
};

/**
 * 卡片悬停动画
 * 为卡片组件提供悬停效果
 */
export const createCardHoverAnimation = (target: string, isHovering: boolean) => {
  return animate(target, {
    y: isHovering ? -4 : 0,
    scale: isHovering ? 1.01 : 1,
    duration: 300,
    ease: 'outQuart',
  });
};

/**
 * 页面加载动画
 * 为页面元素提供渐进式加载效果
 */
export const createPageLoadAnimation = (targets: string[]) => {
  const timeline = createTimeline({
    defaults: {
      ease: 'outExpo',
      duration: 600,
    }
  });

  targets.forEach((target, index) => {
    timeline.add(target, {
      y: { from: 50, to: 0 },
      opacity: { from: 0, to: 1 },
    }, index * 100);
  });

  return timeline;
};

/**
 * 数字计数动画
 * 为数值变化提供平滑的计数效果
 */
export const createCountAnimation = (
  target: HTMLElement,
  from: number,
  to: number,
  duration: number = 1000
) => {
  return animate({
    value: from,
    onUpdate: (self: any) => {
      target.textContent = self.value.toFixed(2);
    }
  }, {
    value: to,
    duration,
    ease: 'outExpo',
  });
};

/**
 * 模态框动画
 * 为模态框提供优雅的显示/隐藏动画
 */
export const createModalAnimation = (
  backdrop: string,
  modal: string,
  isShowing: boolean
) => {
  const timeline = createTimeline({
    defaults: {
      duration: 300,
    }
  });

  if (isShowing) {
    timeline
      .add(backdrop, {
        opacity: { from: 0, to: 1 },
        ease: 'outQuart',
      }, 0)
      .add(modal, {
        scale: { from: 0.8, to: 1 },
        opacity: { from: 0, to: 1 },
        ease: 'outBack(1.7)',
      }, 100);
  } else {
    timeline
      .add(modal, {
        scale: 0.8,
        opacity: 0,
        ease: 'inBack(1.7)',
      }, 0)
      .add(backdrop, {
        opacity: 0,
        ease: 'inQuart',
      }, 100);
  }

  return timeline;
};

/**
 * 列表项动画
 * 为列表项提供交错动画效果
 */
export const createListAnimation = (items: string) => {
  return animate(items, {
    y: { from: 30, to: 0 },
    opacity: { from: 0, to: 1 },
    duration: 500,
    ease: 'outExpo',
    delay: (el: Element, i: number) => i * 100,
  });
};

/**
 * 通用缓动函数
 * 提供常用的缓动效果
 */
export const easings = {
  smooth: 'outExpo',
  bounce: 'outElastic(1, .8)',
  quick: 'outQuart',
  gentle: 'inOutQuart',
  dramatic: 'outBack(1.7)',
} as const;

/**
 * 性能优化工具
 * 提供动画性能监控和优化
 */
export const animationUtils = {
  /**
   * 批量执行动画以优化性能
   */
  batch: (animations: (() => any)[]) => {
    const timeline = createTimeline();
    animations.forEach((anim, index) => {
      timeline.add(anim(), index * 50);
    });
    return timeline;
  },

  /**
   * 检查是否支持硬件加速
   */
  supportsHardwareAcceleration: () => {
    const testEl = document.createElement('div');
    testEl.style.transform = 'translateZ(0)';
    return testEl.style.transform !== '';
  },

  /**
   * 优化动画性能的建议设置
   */
  getOptimalSettings: () => ({
    frameRate: window.devicePixelRatio > 1 ? 60 : 30,
    duration: window.matchMedia('(prefers-reduced-motion: reduce)').matches ? 0 : 500,
  }),
};

/**
 * 创建数字递增动画
 * 为数值显示提供炫酷的递增效果
 */
export const createCountUpAnimation = (element: HTMLElement, targetValue: number, duration: number = 1500): void => {
  const startValue = 0;
  const obj = { value: startValue };

  animate(obj, {
    value: targetValue,
    duration: duration,
    ease: 'outQuart',
    update: () => {
      element.textContent = obj.value.toFixed(2);
    },
    complete: () => {
      element.textContent = targetValue.toFixed(2);
      element.classList.add('animated');
    }
  });
};


