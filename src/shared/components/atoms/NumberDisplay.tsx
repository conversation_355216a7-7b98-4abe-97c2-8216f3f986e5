import React from 'react';
import { formatNumber, NumberPrecision } from '@/shared/utils/format';

interface NumberDisplayProps {
  value: number | string | undefined | null;
  unit?: string;
  className?: string;
  precision?: NumberPrecision;
  fallback?: string;
}

/**
 * 全局数值显示组件
 * 统一处理所有数值的格式化显示，支持单位、精度控制等
 *
 * @param value - 要显示的数值
 * @param unit - 单位（如 kg, cm, kcal 等）
 * @param className - CSS类名
 * @param precision - 精度控制：
 *   - 'auto': 自动去除无意义的.00（默认）
 *   - 'integer': 强制显示为整数
 *   - 'decimal': 强制显示两位小数
 *   - number: 指定小数位数
 * @param fallback - 当值为空时的回退显示
 */
export const NumberDisplay: React.FC<NumberDisplayProps> = ({
  value,
  unit,
  className = '',
  precision = 'auto',
  fallback = '0'
}) => {
  // 使用新的格式化系统
  const formattedText = formatNumber(value, {
    precision,
    unit,
    fallback
  });

  return (
    <span className={className} data-react-component="number-display">
      {formattedText}
    </span>
  );
};

// 预设的常用数值显示组件 - 根据新的格式化规则
export const WeightDisplay: React.FC<Omit<NumberDisplayProps, 'unit' | 'precision'> & { value: number | string }> = (props) => (
  <NumberDisplay {...props} unit="kg" precision="decimal" />
);

export const HeightDisplay: React.FC<Omit<NumberDisplayProps, 'unit' | 'precision'> & { value: number | string }> = (props) => (
  <NumberDisplay {...props} unit="cm" precision="decimal" />
);

export const AgeDisplay: React.FC<Omit<NumberDisplayProps, 'unit' | 'precision'> & { value: number | string }> = (props) => (
  <NumberDisplay {...props} unit="岁" precision="decimal" />
);

export const CalorieDisplay: React.FC<Omit<NumberDisplayProps, 'unit' | 'precision'> & { value: number | string }> = (props) => (
  <span data-react-component="calorie-display">
    <NumberDisplay {...props} unit="kcal" precision="integer" />
  </span>
);

export const BMIDisplay: React.FC<Omit<NumberDisplayProps, 'unit' | 'precision'> & { value: number | string }> = (props) => (
  <NumberDisplay {...props} precision="decimal" />
);

export const DaysDisplay: React.FC<Omit<NumberDisplayProps, 'unit' | 'precision'> & { value: number | string }> = (props) => (
  <NumberDisplay {...props} unit="天" precision="decimal" />
);

export default NumberDisplay;
