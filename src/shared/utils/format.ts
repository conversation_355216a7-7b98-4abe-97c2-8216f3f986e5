import { clsx, type ClassValue } from 'clsx';
import { twMerge } from 'tailwind-merge';

/**
 * 合并Tailwind CSS类名
 */
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * 格式化数字
 */
export function formatNumber(
  value: number,
  options: {
    decimals?: number;
    separator?: string;
    prefix?: string;
    suffix?: string;
  } = {}
): string {
  const { decimals = 0, separator = ',', prefix = '', suffix = '' } = options;
  
  const formatted = value.toFixed(decimals);
  const parts = formatted.split('.');
  
  // 添加千位分隔符
  parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, separator);
  
  return prefix + parts.join('.') + suffix;
}

/**
 * 格式化卡路里（智能小数显示）
 */
export function formatCalories(calories: number): string {
  const formatted = formatDecimal(calories);
  return `${formatted} kcal`;
}

/**
 * 格式化重量（智能小数显示）
 */
export function formatWeight(weight: number, unit: 'g' | 'kg' = 'g'): string {
  const formatted = formatDecimal(weight);
  return `${formatted} ${unit}`;
}

/**
 * 格式化百分比
 */
export function formatPercentage(value: number, decimals: number = 2): string {
  return formatNumber(value * 100, { decimals, suffix: '%' });
}

/**
 * 格式化文件大小
 */
export function formatFileSize(bytes: number): string {
  const sizes = ['B', 'KB', 'MB', 'GB'];
  if (bytes === 0) return '0 B';
  
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  const size = bytes / Math.pow(1024, i);
  
  return formatNumber(size, { decimals: 1, suffix: ` ${sizes[i]}` });
}

/**
 * 截断文本
 */
export function truncateText(text: string, maxLength: number): string {
  if (text.length <= maxLength) return text;
  return text.slice(0, maxLength) + '...';
}

/**
 * 首字母大写
 */
export function capitalize(text: string): string {
  return text.charAt(0).toUpperCase() + text.slice(1);
}

/**
 * 格式化手机号
 */
export function formatPhoneNumber(phone: string): string {
  const cleaned = phone.replace(/\D/g, '');
  const match = cleaned.match(/^(\d{3})(\d{4})(\d{4})$/);
  
  if (match) {
    return `${match[1]} ${match[2]} ${match[3]}`;
  }
  
  return phone;
}

/**
 * 验证并格式化邮箱
 */
export function formatEmail(email: string): string {
  return email.toLowerCase().trim();
}

/**
 * 格式化营养素数值（智能小数显示）
 */
export function formatNutrient(value: number, unit: string): string {
  const formatted = formatDecimal(value);
  return `${formatted} ${unit}`;
}

/**
 * 统一的2位小数格式化函数（智能显示，.00时不显示小数）
 */
export function formatDecimal(value: number): string {
  if (isNaN(value) || !isFinite(value)) {
    return '0';
  }

  const fixed = value.toFixed(2);
  // 使用 parseFloat 移除尾随的零和不必要的小数点
  return parseFloat(fixed).toString();
}

/**
 * 格式化数值并添加单位
 */
export function formatValueWithUnit(value: number, unit: string): string {
  return `${formatDecimal(value)}${unit}`;
}

/**
 * 格式化百分比（2位小数）
 */
export function formatPercentageDecimal(value: number): string {
  return `${formatDecimal(value)}%`;
}

/**
 * 格式化卡路里（2位小数）
 */
export function formatCaloriesDecimal(value: number): string {
  return formatValueWithUnit(value, ' kcal');
}

/**
 * 格式化营养素（2位小数）
 */
export function formatNutrientDecimal(value: number, unit: string): string {
  return formatValueWithUnit(value, unit);
}

/**
 * 格式化代谢数据（2位小数）
 */
export function formatMetabolicData(value: number, unit: string = ' kcal/天'): string {
  return formatValueWithUnit(value, unit);
}