import { clsx, type ClassValue } from 'clsx';
import { twMerge } from 'tailwind-merge';

/**
 * 合并Tailwind CSS类名
 */
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * 旧版格式化数字函数（保留用于千位分隔符）
 * @deprecated 请使用新的 formatNumber 函数
 */
export function formatNumberWithSeparator(
  value: number,
  options: {
    decimals?: number;
    separator?: string;
    prefix?: string;
    suffix?: string;
  } = {}
): string {
  const { decimals = 0, separator = ',', prefix = '', suffix = '' } = options;

  const formatted = value.toFixed(decimals);
  const parts = formatted.split('.');

  // 添加千位分隔符
  parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, separator);

  return prefix + parts.join('.') + suffix;
}

/**
 * 格式化卡路里（智能小数显示）
 * @deprecated 请使用 formatCalorieData 函数
 */
export function formatCalories(calories: number): string {
  return formatCalorieData(calories);
}

/**
 * 格式化重量（智能小数显示）
 * @deprecated 请使用 formatProfileData 函数
 */
export function formatWeight(weight: number, unit: 'g' | 'kg' = 'g'): string {
  return formatProfileData(weight, unit);
}

/**
 * 格式化百分比
 */
export function formatPercentage(value: number, decimals: number = 2): string {
  return formatNumberWithSeparator(value * 100, { decimals, suffix: '%' });
}

/**
 * 格式化文件大小
 */
export function formatFileSize(bytes: number): string {
  const sizes = ['B', 'KB', 'MB', 'GB'];
  if (bytes === 0) return '0 B';

  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  const size = bytes / Math.pow(1024, i);

  return formatNumberWithSeparator(size, { decimals: 1, suffix: ` ${sizes[i]}` });
}

/**
 * 截断文本
 */
export function truncateText(text: string, maxLength: number): string {
  if (text.length <= maxLength) return text;
  return text.slice(0, maxLength) + '...';
}

/**
 * 首字母大写
 */
export function capitalize(text: string): string {
  return text.charAt(0).toUpperCase() + text.slice(1);
}

/**
 * 格式化手机号
 */
export function formatPhoneNumber(phone: string): string {
  const cleaned = phone.replace(/\D/g, '');
  const match = cleaned.match(/^(\d{3})(\d{4})(\d{4})$/);
  
  if (match) {
    return `${match[1]} ${match[2]} ${match[3]}`;
  }
  
  return phone;
}

/**
 * 验证并格式化邮箱
 */
export function formatEmail(email: string): string {
  return email.toLowerCase().trim();
}

/**
 * 格式化营养素数值（智能小数显示）
 * @deprecated 请使用 formatNutritionData 函数
 */
export function formatNutrient(value: number, unit: string): string {
  return formatNutritionData(value, unit);
}

/**
 * 数字格式化精度类型
 */
export type NumberPrecision = 'integer' | 'decimal' | 'auto' | number;

/**
 * 数字格式化配置
 */
export interface NumberFormatConfig {
  precision: NumberPrecision;
  unit?: string;
  prefix?: string;
  suffix?: string;
  fallback?: string;
}

/**
 * 核心数字格式化函数
 * 根据精度类型进行不同的格式化处理
 */
export function formatNumber(value: number | null | undefined, config: NumberFormatConfig): string {
  // 处理空值和无效值
  if (value === null || value === undefined || isNaN(value) || !isFinite(value)) {
    const fallback = config.fallback || '0';
    return `${config.prefix || ''}${fallback}${config.unit ? ` ${config.unit}` : ''}${config.suffix || ''}`;
  }

  let formattedValue: string;

  switch (config.precision) {
    case 'integer':
      // 强制显示为整数
      formattedValue = Math.round(value).toString();
      break;

    case 'decimal':
      // 强制显示两位小数
      formattedValue = value.toFixed(2);
      break;

    case 'auto':
      // 智能显示：整数不显示小数，小数保留有效位
      formattedValue = value % 1 === 0 ? value.toString() : parseFloat(value.toFixed(2)).toString();
      break;

    default:
      // 指定小数位数
      if (typeof config.precision === 'number') {
        formattedValue = value.toFixed(config.precision);
        // 如果是0位小数，等同于integer
        if (config.precision === 0) {
          formattedValue = Math.round(value).toString();
        }
      } else {
        // 默认使用auto模式
        formattedValue = value % 1 === 0 ? value.toString() : parseFloat(value.toFixed(2)).toString();
      }
  }

  return `${config.prefix || ''}${formattedValue}${config.unit ? ` ${config.unit}` : ''}${config.suffix || ''}`;
}

/**
 * 预设格式化函数 - 个人档案数据（保持两位小数）
 */
export function formatProfileData(value: number | null | undefined, unit?: string): string {
  return formatNumber(value, { precision: 'decimal', unit });
}

/**
 * 预设格式化函数 - 健康数据（保持两位小数）
 */
export function formatHealthData(value: number | null | undefined, unit?: string): string {
  return formatNumber(value, { precision: 'decimal', unit });
}

/**
 * 预设格式化函数 - 卡路里数据（显示整数）
 */
export function formatCalorieData(value: number | null | undefined): string {
  return formatNumber(value, { precision: 'integer', unit: 'kcal' });
}

/**
 * 预设格式化函数 - 营养数据（显示整数）
 */
export function formatNutritionData(value: number | null | undefined, unit: string): string {
  return formatNumber(value, { precision: 'integer', unit });
}

/**
 * 预设格式化函数 - 代谢数据（保留一位小数）
 */
export function formatMetabolicValue(value: number | null | undefined, unit?: string): string {
  return formatNumber(value, { precision: 1, unit });
}

/**
 * 兼容性函数 - 保持向后兼容
 * @deprecated 请使用新的 formatNumber 函数
 */
export function formatDecimal(value: number): string {
  return formatNumber(value, { precision: 'auto' });
}

/**
 * 格式化数值并添加单位
 * @deprecated 请使用新的 formatNumber 函数
 */
export function formatValueWithUnit(value: number, unit: string): string {
  return formatNumber(value, { precision: 'auto', unit });
}

/**
 * 格式化百分比（2位小数）
 * @deprecated 请使用新的 formatNumber 函数
 */
export function formatPercentageDecimal(value: number): string {
  return formatNumber(value, { precision: 'auto', suffix: '%' });
}

/**
 * 格式化卡路里（2位小数）
 * @deprecated 请使用 formatCalorieData 函数
 */
export function formatCaloriesDecimal(value: number): string {
  return formatCalorieData(value);
}

/**
 * 格式化营养素（2位小数）
 * @deprecated 请使用 formatNutritionData 函数
 */
export function formatNutrientDecimal(value: number, unit: string): string {
  return formatNutritionData(value, unit);
}

/**
 * 格式化代谢数据（2位小数）
 * @deprecated 请使用 formatMetabolicValue 函数
 */
export function formatMetabolicData(value: number, unit: string = 'kcal/天'): string {
  return formatMetabolicValue(value, unit);
}