import React from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  Filler
} from 'chart.js';
import { Line, Bar, Doughnut } from 'react-chartjs-2';
import { NutritionAnalysis } from '@/shared/types';
import { formatDate } from '@/shared/utils';
import { formatDecimal } from '@/shared/utils/format';

// 注册Chart.js组件
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

interface CalorieTrendChartProps {
  data: NutritionAnalysis;
  height?: number;
}

export const CalorieTrendChart: React.FC<CalorieTrendChartProps> = ({ 
  data, 
  height = 300 
}) => {
  const chartData = {
    labels: data.trends.map(trend => formatDate(trend.date, 'MM/dd')),
    datasets: [
      {
        label: '实际摄入',
        data: data.trends.map(trend => trend.calories),
        borderColor: '#10b981',
        backgroundColor: 'rgba(16, 185, 129, 0.1)',
        fill: true,
        tension: 0.4,
        pointRadius: 4,
        pointHoverRadius: 6
      },
      {
        label: '目标摄入',
        data: data.trends.map(() => data.calories.target),
        borderColor: '#6b7280',
        backgroundColor: 'transparent',
        borderDash: [5, 5],
        fill: false,
        pointRadius: 0,
        pointHoverRadius: 0
      }
    ]
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
        labels: {
          usePointStyle: true,
          padding: 20
        }
      },
      tooltip: {
        mode: 'index' as const,
        intersect: false,
        callbacks: {
          label: (context: any) => {
            return `${context.dataset.label}: ${context.parsed.y} kcal`;
          }
        }
      }
    },
    scales: {
      x: {
        grid: {
          display: false
        }
      },
      y: {
        beginAtZero: true,
        grid: {
          color: 'rgba(0, 0, 0, 0.1)'
        },
        ticks: {
          callback: (value: any) => `${value} kcal`
        }
      }
    },
    interaction: {
      mode: 'nearest' as const,
      axis: 'x' as const,
      intersect: false
    }
  };

  return (
    <div style={{ height }}>
      <Line data={chartData} options={options} />
    </div>
  );
};

interface MealDistributionChartProps {
  breakfast: number;
  lunch: number;
  dinner: number;
  snack?: number;
  size?: number;
}

export const MealDistributionChart: React.FC<MealDistributionChartProps> = ({
  breakfast,
  lunch,
  dinner,
  snack = 0,
  size = 200
}) => {
  const total = breakfast + lunch + dinner + snack;
  
  if (total === 0) {
    return (
      <div 
        className="flex items-center justify-center text-gray-500 text-sm"
        style={{ width: size, height: size }}
      >
        暂无数据
      </div>
    );
  }

  const data = {
    labels: snack > 0 ? ['早餐', '午餐', '晚餐', '零食'] : ['早餐', '午餐', '晚餐'],
    datasets: [
      {
        data: snack > 0 ? [breakfast, lunch, dinner, snack] : [breakfast, lunch, dinner],
        backgroundColor: [
          '#fbbf24', // 早餐 - 黄色
          '#10b981', // 午餐 - 绿色
          '#3b82f6', // 晚餐 - 蓝色
          '#8b5cf6'  // 零食 - 紫色
        ],
        borderColor: [
          '#f59e0b',
          '#059669',
          '#2563eb',
          '#7c3aed'
        ],
        borderWidth: 2,
        hoverBorderWidth: 3
      }
    ]
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'bottom' as const,
        labels: {
          padding: 15,
          usePointStyle: true,
          font: {
            size: 12
          }
        }
      },
      tooltip: {
        callbacks: {
          label: (context: any) => {
            const percentage = formatDecimal((context.parsed / total) * 100);
            return `${context.label}: ${context.parsed} kcal (${percentage}%)`;
          }
        }
      }
    },
    cutout: '50%'
  };

  return (
    <div style={{ width: size, height: size }}>
      <Doughnut data={data} options={options} />
    </div>
  );
};

interface WeeklyComparisonChartProps {
  data: { day: string; current: number; target: number }[];
  height?: number;
}

export const WeeklyComparisonChart: React.FC<WeeklyComparisonChartProps> = ({
  data,
  height = 250
}) => {
  const chartData = {
    labels: data.map(item => item.day),
    datasets: [
      {
        label: '实际摄入',
        data: data.map(item => item.current),
        backgroundColor: 'rgba(16, 185, 129, 0.8)',
        borderColor: '#10b981',
        borderWidth: 1,
        borderRadius: 4,
        borderSkipped: false
      },
      {
        label: '目标摄入',
        data: data.map(item => item.target),
        backgroundColor: 'rgba(107, 114, 128, 0.3)',
        borderColor: '#6b7280',
        borderWidth: 1,
        borderRadius: 4,
        borderSkipped: false
      }
    ]
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
        labels: {
          usePointStyle: true,
          padding: 20
        }
      },
      tooltip: {
        callbacks: {
          label: (context: any) => {
            return `${context.dataset.label}: ${context.parsed.y} kcal`;
          }
        }
      }
    },
    scales: {
      x: {
        grid: {
          display: false
        }
      },
      y: {
        beginAtZero: true,
        grid: {
          color: 'rgba(0, 0, 0, 0.1)'
        },
        ticks: {
          callback: (value: any) => `${value} kcal`
        }
      }
    }
  };

  return (
    <div style={{ height }}>
      <Bar data={chartData} options={options} />
    </div>
  );
};

interface NutrientBreakdownChartProps {
  protein: number;
  fat: number;
  carbs: number;
  size?: number;
}

export const NutrientBreakdownChart: React.FC<NutrientBreakdownChartProps> = ({
  protein,
  fat,
  carbs,
  size = 180
}) => {
  const total = protein + fat + carbs;
  
  if (total === 0) {
    return (
      <div 
        className="flex items-center justify-center text-gray-500 text-sm"
        style={{ width: size, height: size }}
      >
        暂无数据
      </div>
    );
  }

  const data = {
    labels: ['蛋白质', '脂肪', '碳水化合物'],
    datasets: [
      {
        data: [protein, fat, carbs],
        backgroundColor: [
          '#ef4444', // 蛋白质 - 红色
          '#f59e0b', // 脂肪 - 橙色
          '#10b981'  // 碳水化合物 - 绿色
        ],
        borderColor: [
          '#dc2626',
          '#d97706',
          '#059669'
        ],
        borderWidth: 2
      }
    ]
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'bottom' as const,
        labels: {
          padding: 10,
          usePointStyle: true,
          font: {
            size: 11
          }
        }
      },
      tooltip: {
        callbacks: {
          label: (context: any) => {
            const percentage = formatDecimal((context.parsed / total) * 100);
            return `${context.label}: ${context.parsed}g (${percentage}%)`;
          }
        }
      }
    },
    cutout: '60%'
  };

  return (
    <div style={{ width: size, height: size }}>
      <Doughnut data={data} options={options} />
    </div>
  );
};