import React from 'react';
import { DailySummary, UserProfile } from '@/shared/types';
import { formatDecimal } from '@/shared/utils/format';
import { formatCalories } from '@/shared/utils';

interface NutritionAdviceProps {
  summary: DailySummary;
  profile: UserProfile;
}

interface Advice {
  type: 'success' | 'warning' | 'info' | 'tip';
  title: string;
  message: string;
  emoji: string;
  color: string;
  bgColor: string;
}

const NutritionAdvice: React.FC<NutritionAdviceProps> = ({ summary, profile }) => {
  const generateAdvice = (): Advice[] => {
    const advice: Advice[] = [];
    const percentage = (summary.totalCalories / summary.calorieLimit) * 100;
    const remaining = summary.remainingCalories;

    // 卡路里摄入建议
    if (percentage < 70) {
      advice.push({
        type: 'warning',
        title: '卡路里摄入不足',
        message: `您今天只摄入了目标的${Math.round(percentage)}%，建议增加健康食物的摄入。`,
        emoji: '⚠️',
        color: 'text-orange-800',
        bgColor: 'from-orange-100/60 to-amber-100/60'
      });
    } else if (percentage >= 90 && percentage <= 110) {
      advice.push({
        type: 'success',
        title: '卡路里摄入理想',
        message: '您的卡路里摄入量非常理想，继续保持！',
        emoji: '✅',
        color: 'text-green-800',
        bgColor: 'from-green-100/60 to-emerald-100/60'
      });
    } else if (percentage > 130) {
      advice.push({
        type: 'warning',
        title: '卡路里摄入过量',
        message: `您已超出目标${Math.round(percentage - 100)}%，建议明天适当减少摄入。`,
        emoji: '🚨',
        color: 'text-red-800',
        bgColor: 'from-red-100/60 to-rose-100/60'
      });
    }

    // 三餐分布建议
    const { breakfast, dinner } = summary.mealBreakdown;

    if (breakfast.calories === 0) {
      advice.push({
        type: 'tip',
        title: '别忘记早餐',
        message: '早餐是一天中最重要的一餐，建议摄入300-500卡路里。',
        emoji: '🌅',
        color: 'text-amber-800',
        bgColor: 'from-amber-100/60 to-yellow-100/60'
      });
    }

    if (dinner.calories > dinner.calorieLimit * 1.5) {
      advice.push({
        type: 'info',
        title: '晚餐摄入较多',
        message: '晚餐摄入过多可能影响睡眠和消化，建议适当减少。',
        emoji: '🌙',
        color: 'text-orange-800',
        bgColor: 'from-orange-100/60 to-amber-100/60'
      });
    }

    // 营养素建议
    const { protein, carbs, fat } = summary.nutrition;
    const totalMacros = protein + carbs + fat;

    if (totalMacros > 0) {
      const proteinPercentage = (protein / totalMacros) * 100;

      if (proteinPercentage < 15) {
        advice.push({
          type: 'tip',
          title: '增加蛋白质摄入',
          message: '蛋白质摄入偏低，建议增加瘦肉、鱼类、豆类等高蛋白食物。',
          emoji: '🥩',
          color: 'text-amber-800',
          bgColor: 'from-yellow-100/60 to-orange-100/60'
        });
      }
    }

    // 基于用户目标的建议
    if (profile.targetWeight < profile.weight) {
      // 减重目标
      if (remaining > 0 && remaining < 200) {
        advice.push({
          type: 'success',
          title: '减重进展良好',
          message: `还剩${formatDecimal(remaining)}卡路里，可以选择一些低卡零食。`,
          emoji: '🎯',
          color: 'text-green-800',
          bgColor: 'from-green-100/60 to-emerald-100/60'
        });
      }
    }

    // 水分摄入提醒
    advice.push({
      type: 'info',
      title: '记得补充水分',
      message: '建议每天饮水1.5-2升，有助于新陈代谢和减重。',
      emoji: '💧',
      color: 'text-amber-800',
      bgColor: 'from-amber-100/60 to-yellow-100/60'
    });

    return advice;
  };

  const adviceList = generateAdvice();

  return (
    <div className="space-y-3">
      {adviceList.map((advice, index) => (
        <div
          key={index}
          className="flex items-start gap-3 py-3 border-b border-amber-200/30 last:border-b-0"
        >
          {/* Emoji图标 */}
          <div className="flex-shrink-0 w-6 h-6 flex items-center justify-center">
            <span className="text-base">{advice.emoji}</span>
          </div>

          {/* 内容 */}
          <div className="flex-1 min-w-0">
            <h4 className="font-semibold text-amber-900 text-sm mb-1">
              {advice.title}
            </h4>
            <p className="text-xs text-amber-800/80 leading-relaxed">
              {advice.message}
            </p>
          </div>
        </div>
      ))}

      {adviceList.length === 0 && (
        <div className="text-center py-4">
          <span className="text-lg mb-2 block">💡</span>
          <p className="text-amber-800/60 text-sm">暂无营养建议</p>
        </div>
      )}
    </div>
  );
};

export default NutritionAdvice;